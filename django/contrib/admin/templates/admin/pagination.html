{% load admin_list %}
{% load i18n %}
<nav class="paginator" aria-labelledby="pagination">
    <h2 id="pagination" class="visually-hidden">{% blocktranslate with name=cl.opts.verbose_name_plural %}Pagination {{ name }}{% endblocktranslate %}</h2>
    {% if pagination_required %}
    <ul>
    {% for i in page_range %}
        <li>{% paginator_number cl i %}</li>
    {% endfor %}
    </ul>
    {% endif %}
{{ cl.result_count }} {% if cl.result_count == 1 %}{{ cl.opts.verbose_name }}{% else %}{{ cl.opts.verbose_name_plural }}{% endif %}
{% if show_all_url %}<a href="{{ show_all_url }}" class="showall">{% translate 'Show all' %}</a>{% endif %}
</nav>
