# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2012
# <PERSON> <<EMAIL>>, 2021,2023-2025
# <PERSON> <<EMAIL>>, 2013-2017,2019
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# 85794379431c3e0f5c85c0e72a78d45b_658ddd9, 2013
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 11:30-0500\n"
"PO-Revision-Date: 2025-04-01 15:04-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021,2023-2025\n"
"Language-Team: Danish (http://app.transifex.com/django/django/language/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Personal info"
msgstr "Personlig information"

msgid "Permissions"
msgstr "Rettigheder"

msgid "Important dates"
msgstr "Vigtige datoer"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "Der findes ikke et %(name)s-objekt med primærnøgle %(key)r."

msgid "Conflicting form data submitted. Please try again."
msgstr "Uoverensstemmende formulardata indsendt. Prøv igen."

msgid "Password changed successfully."
msgstr "Adgangskoden blev ændret."

msgid "Password-based authentication was disabled."
msgstr "Adgangskodebaseret autentificering blev deaktiveret."

#, python-format
msgid "Change password: %s"
msgstr "Skift adgangskode: %s"

#, python-format
msgid "Set password: %s"
msgstr "Sæt adgangskode: %s"

msgid "Authentication and Authorization"
msgstr "Godkendelse og autorisation"

msgid "password"
msgstr "adgangskode"

msgid "last login"
msgstr "sidst logget ind"

msgid "Invalid password format or unknown hashing algorithm."
msgstr "Ugyldigt adgangskodeformat eller hashing-algoritme."

msgid "No password set."
msgstr "Ingen adgangskode valgt."

msgid "Reset password"
msgstr "Nulstil adgangskode"

msgid "Set password"
msgstr "Sæt adgangskode"

msgid "The two password fields didn’t match."
msgstr "De to adgangskoder var ikke identiske."

msgid "Password"
msgstr "Adgangskode"

msgid "Password confirmation"
msgstr "Bekræftelse af adgangskode"

msgid "Enter the same password as before, for verification."
msgstr "Indtast den samme adgangskode som før, for bekræftelse."

msgid ""
"Whether the user will be able to authenticate using a password or not. If "
"disabled, they may still be able to authenticate using other backends, such "
"as Single Sign-On or LDAP."
msgstr ""
"Om brugeren kan logge ind med adgangskode eller ej. Hvis deaktiveret kan "
"brugeren muligvis stadig logge ind vha. andre backends så som Single Sign-On "
"eller LDAP."

msgid "Password-based authentication"
msgstr "Adgangskodebaseret autentificering"

msgid "Enabled"
msgstr "Aktiveret"

msgid "Disabled"
msgstr "Deaktiveret"

msgid ""
"Raw passwords are not stored, so there is no way to see the user’s password."
msgstr ""
"Rå adgangskoder gemmes ikke, så det er ikke muligt at se brugerens "
"adgangskode."

msgid ""
"Enable password-based authentication for this user by setting a password."
msgstr ""
"Aktivér adgangskodebaseret autentificering for denne bruger ved at sætte en "
"adgangskode."

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"Indtast venligst korrekt %(username)s og adgangskode. Bemærk at begge felter "
"kan være versalfølsomme."

msgid "This account is inactive."
msgstr "Denne konto er inaktiv."

msgid "Email"
msgstr "E-mail"

msgid "New password"
msgstr "Ny adgangskode"

msgid "New password confirmation"
msgstr "Bekræftelse af ny adgangskode"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr ""
"Din gamle adgangskode blev ikke indtastet korrekt. Indtast den venligst igen."

msgid "Old password"
msgstr "Gammel adgangskode"

msgid "algorithm"
msgstr "algoritme"

msgid "iterations"
msgstr "iterationer"

msgid "salt"
msgstr "salt"

msgid "hash"
msgstr "hash"

msgid "variety"
msgstr "variation"

msgid "version"
msgstr "version"

msgid "memory cost"
msgstr "hukommelsesomkostning"

msgid "time cost"
msgstr "tidsomkostning"

msgid "parallelism"
msgstr "parallelitet"

msgid "work factor"
msgstr "work factor"

msgid "checksum"
msgstr "tjeksum"

msgid "block size"
msgstr "blokstørrelse"

msgid "name"
msgstr "navn"

msgid "content type"
msgstr "indholdstype"

msgid "codename"
msgstr "kodenavn"

msgid "permission"
msgstr "rettighed"

msgid "permissions"
msgstr "rettigheder"

msgid "group"
msgstr "gruppe"

msgid "groups"
msgstr "grupper"

msgid "superuser status"
msgstr "superbrugerstatus"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr ""
"Bestemmer at denne bruger har alle rettigheder uden at tildele dem eksplicit."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""
"Grupperne som denne bruger hører til. En bruger får alle rettigheder givet "
"til hver af hans/hendes grupper."

msgid "user permissions"
msgstr "rettigheder"

msgid "Specific permissions for this user."
msgstr "Specifikke rettigheder for denne bruger."

msgid "username"
msgstr "brugernavn"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr "Påkrævet. Højst 150 tegn. Kun bogstaver og cifre samt @/./+/-/_"

msgid "A user with that username already exists."
msgstr "En bruger med dette brugernavn findes allerede."

msgid "first name"
msgstr "fornavn"

msgid "last name"
msgstr "efternavn"

msgid "email address"
msgstr "e-mail-adresse"

msgid "staff status"
msgstr "admin-status"

msgid "Designates whether the user can log into this admin site."
msgstr "Bestemmer om brugeren kan logge ind på dette administrationswebsite."

msgid "active"
msgstr "aktiv"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"Bestemmer om brugeren skal behandles som aktiv. Fravælg dette frem for at "
"slette en konto."

msgid "date joined"
msgstr "dato for registrering"

msgid "user"
msgstr "bruger"

msgid "users"
msgstr "brugere"

#, python-format
msgid "This password is too short. It must contain at least %d character."
msgid_plural ""
"This password is too short. It must contain at least %d characters."
msgstr[0] "Denne adgangskode er for kort. Den skal indeholde mindst %d tegn."
msgstr[1] "Denne adgangskode er for kort. Den skal indeholde mindst %d tegn."

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] "Din adgangskode skal indeholde mindst %(min_length)d tegn."
msgstr[1] "Din adgangskode skal indeholde mindst %(min_length)d tegn."

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr "Din adgangskode minder for meget om din/dit %(verbose_name)s."

msgid "Your password can’t be too similar to your other personal information."
msgstr "Din adgangskode må ikke minde om dine andre personlige oplysninger."

msgid "This password is too common."
msgstr "Denne adgangskode er for almindelig."

msgid "Your password can’t be a commonly used password."
msgstr "Din adgangskode må ikke være en ofte anvendt adgangskode."

msgid "This password is entirely numeric."
msgstr "Denne adgangskode er udelukkende numerisk."

msgid "Your password can’t be entirely numeric."
msgstr "Din adgangskode må ikke være udelukkende numerisk."

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "Adgangskode nulstillet på %(site_name)s"

msgid ""
"Enter a valid username. This value may contain only unaccented lowercase a-z "
"and uppercase A-Z letters, numbers, and @/./+/-/_ characters."
msgstr ""
"Indtast et gyldigt brugernavn. Denne værdi må kun indeholde små bogstaver a-"
"z og store bogstaver A-Z uden accenter, samt cifre og tegnene @/./+/-/_."

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""
"Indtast et gyldigt brugernavn. Denne værdi må kun indeholde bogstaver, cifre "
"og tegnene @/./+/-/_."

msgid "Logged out"
msgstr "Logget ud"

msgid "Password reset"
msgstr "Nulstilling af adgangskode"

msgid "Password reset sent"
msgstr "Nulstilling af kodeord sendt"

msgid "Enter new password"
msgstr "Indtast ny adgangskode"

msgid "Password reset unsuccessful"
msgstr "Adgangskoden blev ikke nulstillet"

msgid "Password reset complete"
msgstr "Nulstilling af adgangskode fuldført"

msgid "Password change"
msgstr "Ændring af adgangskode"

msgid "Password change successful"
msgstr "Adgangskoden blev ændret"
