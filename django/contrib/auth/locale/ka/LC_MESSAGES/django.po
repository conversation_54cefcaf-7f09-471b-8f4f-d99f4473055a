# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2013-2015
# Demetre Barbakadze, 2023
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-17 03:19-0500\n"
"PO-Revision-Date: 2023-12-04 08:09+0000\n"
"Last-Translator: Demetre Barbakadze, 2023\n"
"Language-Team: Georgian (http://app.transifex.com/django/django/language/"
"ka/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ka\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

msgid "Personal info"
msgstr "პირადი ინფორმაცია"

msgid "Permissions"
msgstr "უფლებები"

msgid "Important dates"
msgstr "მნიშვნელოვანი თარიღები"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "%(name)s ობიექტი პირველადი გასაღებით %(key)r არ არსებობს."

msgid "Password changed successfully."
msgstr "პაროლი წარმატებით შეიცვალა."

#, python-format
msgid "Change password: %s"
msgstr "შევცვალოთ პაროლი: %s"

msgid "Authentication and Authorization"
msgstr "იდენტიფიკაცია და ავტორიზაცია"

msgid "password"
msgstr "პაროლი"

msgid "last login"
msgstr "ბოლო შესვლა"

msgid "No password set."
msgstr "არ არის დაყენებული პაროლი."

msgid "Invalid password format or unknown hashing algorithm."
msgstr "არასწორი პაროლის ფორმატი ან უცნობი ჰეშირების ალგორითმი."

msgid "The two password fields didn’t match."
msgstr "ორი პაროლის ველი ერთმანეთს არ ემთხვევა"

msgid "Password"
msgstr "პაროლი"

msgid "Password confirmation"
msgstr "პაროლის დამოწმება"

msgid "Enter the same password as before, for verification."
msgstr "გაიმეორეთ იგივე პაროლი ვერიფიკაციისთვის."

msgid ""
"Raw passwords are not stored, so there is no way to see this user’s "
"password, but you can change the password using <a href=\"{}\">this form</a>."
msgstr ""

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"გთხოვთ, შეიყვანოთ სწორი %(username)s და პაროლი. იქონიეთ მხედველობაში, რომ "
"ორივე ველი ითვალისწინებს მთავრულს."

msgid "This account is inactive."
msgstr "თქვენი ანგარიში არააქტიურია."

msgid "Email"
msgstr "ელ. ფოსტა"

msgid "New password"
msgstr "ახალი პაროლი"

msgid "New password confirmation"
msgstr "ახალი პაროლის დამოწმება"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr "თქვენი ძველი პაროლი შეყვანილია არასწორად. გთხოვთ, შეიყვანოთ ხელახლა."

msgid "Old password"
msgstr "ძველი პაროლი"

msgid "Password (again)"
msgstr "პაროლი (განმეორებით)"

msgid "algorithm"
msgstr "ალგორითმი"

msgid "iterations"
msgstr "იტერაციები"

msgid "salt"
msgstr "მარილი"

msgid "hash"
msgstr "ჰეში"

msgid "variety"
msgstr "მრავალფეროვნება"

msgid "version"
msgstr "ვერსია"

msgid "memory cost"
msgstr "საჭირო მეხსიერების რაოდენობა"

msgid "time cost"
msgstr "საჭირო დროის რაოდენობა"

msgid "parallelism"
msgstr "პარალელიზმი"

msgid "work factor"
msgstr "სამუშაო ფაქტორი"

msgid "checksum"
msgstr "საკონტროლო ჯამი"

msgid "block size"
msgstr ""

msgid "name"
msgstr "სახელი"

msgid "content type"
msgstr "კონტენტის ტიპი"

msgid "codename"
msgstr "კოდური სახელი"

msgid "permission"
msgstr "უფლება"

msgid "permissions"
msgstr "უფლებები"

msgid "group"
msgstr "ჯგუფი"

msgid "groups"
msgstr "ჯგუფები"

msgid "superuser status"
msgstr "სუპერმომხმარებლის სტატუსი"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr "განსაზღვრავს, რომ ამ მომხმარებელს აქვს ყველა უფლება."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""
"ჯგუფები, რომლებსაც მიეკუთვნება ეს მომხმარებელი. მომხმარებელი მიიღებს ყველა "
"უფლებას, რომელიც მინიჭებული აქვს მის თითოეულ ჯგუფს."

msgid "user permissions"
msgstr "მომხმარებლის უფლებები"

msgid "Specific permissions for this user."
msgstr "სპეციფიური უფლებები ამ მომხმარებლისთვის."

msgid "username"
msgstr "მომხმარებლის სახელი"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""
"აუცილებელი. 150 სიმბოლ ან ნაკლები. მხოლოდ ასოები, ციფრები ან @/./+/-/_ ."

msgid "A user with that username already exists."
msgstr "მომხმარებელი ამ სახელით უკვე არსებობს."

msgid "first name"
msgstr "სახელი"

msgid "last name"
msgstr "გვარი"

msgid "email address"
msgstr "ელ. ფოსტის მისამართი"

msgid "staff status"
msgstr "თანამშრომლობის სტატუსი"

msgid "Designates whether the user can log into this admin site."
msgstr ""
"განსაზღვრავს, აქვს თუ არა მომხმარებელს ადმინისტრირების საიტზე შესვლის უფლება."

msgid "active"
msgstr "აქტიურია"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"განსაზღვრავს, რომ მომხმარებელი გააქტიურებულია. მომხმარებლის წაშლის მაგივრად "
"გადანიშნეთ ეს დროშა."

msgid "date joined"
msgstr "გაწევრიანების თარიღი"

msgid "user"
msgstr "მომხმარებელი"

msgid "users"
msgstr "მომხმარებლები"

#, python-format
msgid ""
"This password is too short. It must contain at least %(min_length)d "
"character."
msgid_plural ""
"This password is too short. It must contain at least %(min_length)d "
"characters."
msgstr[0] ""
"პაროლი ძალიან მოკლეა. მინიმუმ %(min_length)dსიმბოლოს უნდა შეიცავდეს."
msgstr[1] ""
"პაროლი ძალიან მოკლეა. მინიმუმ %(min_length)dსიმბოლოს უნდა შეიცავდეს."

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] ""
"პაროლი ძალიან მოკლეა. მინიმუმ %(min_length)d სიმბოლოს უნდა შეიცავდეს."
msgstr[1] ""
"პაროლი ძალიან მოკლეა. მინიმუმ %(min_length)d სიმბოლოს უნდა შეიცავდეს."

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr "პაროლი ძალიან გავს %(verbose_name)s."

msgid "Your password can’t be too similar to your other personal information."
msgstr "პაროლი ძალიან გავს თქვენს პერსონალურ ინფორმაციას."

msgid "This password is too common."
msgstr "ეს პაროლი ხშირად გამოიყენება."

msgid "Your password can’t be a commonly used password."
msgstr "თქვენი პაროლი არ უნდა იყოს ხშირად გამოყენებადი."

msgid "This password is entirely numeric."
msgstr "პაროლი მხოლოდ ციფრებისგან შედგება."

msgid "Your password can’t be entirely numeric."
msgstr "თქვენი პაროლი არ შეიძლება შეიცავდეს მხოლოდ ციფრებს."

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "პაროლის თავიდან დაყენება %(site_name)s-ზე"

msgid ""
"Enter a valid username. This value may contain only unaccented lowercase a-z "
"and uppercase A-Z letters, numbers, and @/./+/-/_ characters."
msgstr ""

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""

msgid "Logged out"
msgstr "გამოსული ხართ"

msgid "Password reset"
msgstr "პაროლის აღდგენა"

msgid "Password reset sent"
msgstr "პაროლის აღდგენა გაგზავნილია"

msgid "Enter new password"
msgstr "შეიყვანეთ ახალი პაროლი"

msgid "Password reset unsuccessful"
msgstr "პაროლის აღდგენა წარუმატებლად დასრულდა"

msgid "Password reset complete"
msgstr "პაროლის აღდგენა დასრულებულია"

msgid "Password change"
msgstr "პაროლის შეცვლა"

msgid "Password change successful"
msgstr "პაროლი წარმატებით შეიცვალა"
