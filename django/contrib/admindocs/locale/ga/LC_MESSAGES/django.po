# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2024-10-07 20:19+0000\n"
"Last-Translator: Ain<PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Irish (http://app.transifex.com/django/django/language/ga/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ga\n"
"Plural-Forms: nplurals=5; plural=(n==1 ? 0 : n==2 ? 1 : n<7 ? 2 : n<11 ? 3 : "
"4);\n"

msgid "Administrative Documentation"
msgstr "Doiciméadúch<PERSON>"

msgid "Home"
msgstr "Baile"

msgid "Documentation"
msgstr "Doiciméadúchán"

msgid "Bookmarklets"
msgstr "Leabharmharcín"

msgid "Documentation bookmarklets"
msgstr "Leabharmharcín doiciméadúchán"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Chun leabharmharcanna a shuiteáil, tarraing an nasc chuig do bharra uirlisí "
"leabharmharcanna, nó deaschliceáil ar an nasc agus cuir le do "
"leabharmharcanna é. Anois is féidir leat an leabharmharc a roghnú ó aon "
"leathanach ar an suíomh."

msgid "Documentation for this page"
msgstr "Doiciméadúchán le hadhaigh an leathanach seo"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Tógann se tusa ón aon leathanach go dtí an doiciméadúchán le hadhaigh an "
"radharc a rinne an leathanach."

msgid "Tags"
msgstr "Clibeanna"

msgid "List of all the template tags and their functions."
msgstr "Liosta de na clibeanna teimpléid go léir agus a bhfeidhmeanna."

msgid "Filters"
msgstr "Scagairí"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Is gníomhartha iad na scagairí is féidir a chur i bhfeidhm ar athróga i "
"dteimpléad chun an t-aschur a athrú."

msgid "Models"
msgstr "Samhla"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Is éard atá i múnlaí ná cur síos ar na réada go léir sa chóras agus ar na "
"réimsí a bhaineann leo. Tá liosta réimsí ag gach samhail ar féidir rochtain "
"a fháil orthu mar athróga teimpléid"

msgid "Views"
msgstr "Radharcanna"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Gintear gach leathanach ar an suíomh poiblí trí radharc. Sainmhíníonn an t-"
"amharc cén teimpléad a úsáidtear chun an leathanach a ghiniúint agus na "
"rudaí atá ar fáil don teimpléad sin."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Uirlisí do do bhrabhsálaí chun rochtain tapa a fháil ar fheidhmiúlacht "
"riaracháin."

msgid "Please install docutils"
msgstr "Suiteáil docutils le do thoil"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a "
"href=\"%(link)s\">docutils</a> library."
msgstr ""
"Teastaíonn leabharlann Python <a href=\"%(link)s\">docutils</a> ón gcóras "
"doiciméadúcháin riaracháin."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr "Iarr ar do riarthóirí <a href=\"%(link)s\">docutils</a> a shuiteáil."

#, python-format
msgid "Model: %(name)s"
msgstr "Múnla: %(name)s"

msgid "Fields"
msgstr "Réimsí"

msgid "Field"
msgstr "Réimse"

msgid "Type"
msgstr "Cineál"

msgid "Description"
msgstr "Cur síos"

msgid "Methods with arguments"
msgstr "Modhanna le hargóintí"

msgid "Method"
msgstr "Modh"

msgid "Arguments"
msgstr "Argóintí"

msgid "Back to Model documentation"
msgstr "Ar ais chuig an doiciméadú Múnla"

msgid "Model documentation"
msgstr "Doiciméadú múnla"

msgid "Model groups"
msgstr "Grúpaí samhlacha"

msgid "Templates"
msgstr "Teimpléid"

#, python-format
msgid "Template: %(name)s"
msgstr "Teimpléad: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Teimpléad: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Teimpléad cuardaigh cosán <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(níl ann)"

msgid "Back to Documentation"
msgstr "Ar ais go Doiciméadúchán"

msgid "Template filters"
msgstr "Ar ais go Doiciméadúchán"

msgid "Template filter documentation"
msgstr "Doiciméadú scagaire teimpléid"

msgid "Built-in filters"
msgstr "scagairí ionsuite"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Chun na scagairí seo a úsáid, cuir<code>%(code)s</code> i do theimpléad sula "
"n-úsáideann tú an scagaire."

msgid "Template tags"
msgstr "Clibeanna teimpléid"

msgid "Template tag documentation"
msgstr "Doiciméadú clib teimpléad"

msgid "Built-in tags"
msgstr "Insuite i clibeanna"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Chun na clibeanna seo a úsáid, cuir <code>%(code)s</code> i do theimpléad "
"sula n-úsáideann tú an chlib."

#, python-format
msgid "View: %(name)s"
msgstr "Amharc: %(name)s"

msgid "Context:"
msgstr "Comhthéacs:"

msgid "Templates:"
msgstr "Teimpléid:"

msgid "Back to View documentation"
msgstr "Ar ais go dtí Féach ar na doiciméid"

msgid "View documentation"
msgstr "Féach ar cháipéisíocht"

msgid "Jump to namespace"
msgstr "Léim go spás ainm"

msgid "Empty namespace"
msgstr "Ainmspás folamh"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Radhairc de réir ainmspáis %(name)s"

msgid "Views by empty namespace"
msgstr "Radhairc de réir ainmspás folamh"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Féach ar fheidhm: <code>%(full_name)s</code>. Ainm: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "clib:"

msgid "filter:"
msgstr "scag:"

msgid "view:"
msgstr "radharc:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Aip %(app_label)r gan aimsiú"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Ní bhfuarthas samhail %(model_name)r  i bhfeidhmclár %(app_label)r"

msgid "model:"
msgstr "samhail:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "An oibiacht gaolmhara `%(app_label)s.%(data_type)s`"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "Oibiachtí gaolmhara `%(app_label)s.%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "gach %s"

#, python-format
msgid "number of %s"
msgstr "líon %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "Feiceann sé nach bhfuil %s oibiacht urlpattern"
