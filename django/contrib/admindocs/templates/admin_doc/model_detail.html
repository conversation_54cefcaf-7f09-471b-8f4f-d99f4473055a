{% extends "admin/base_site.html" %}
{% load i18n %}

{% block extrahead %}
{{ block.super }}
<style>
.module table { width:100%; }
.module table p { padding: 0; margin: 0; }
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; <a href="{% url 'django-admindocs-docroot' %}">{% translate 'Documentation' %}</a>
&rsaquo; <a href="{% url 'django-admindocs-models-index' %}">{% translate 'Models' %}</a>
&rsaquo; {{ name }}
</div>
{% endblock %}

{% block title %}{% blocktranslate %}Model: {{ name }}{% endblocktranslate %}{% endblock %}

{% block content %}
<div id="content-main">
<h1>{{ name }}</h1>
<h2 class="subhead">{{ summary }}</h2>

{{ description }}

<h3>{% translate 'Fields' %}</h3>
<div class="module">
<table class="model">
<thead>
<tr>
    <th scope="col">{% translate 'Field' %}</th>
    <th scope="col">{% translate 'Type' %}</th>
    <th scope="col">{% translate 'Description' %}</th>
</tr>
</thead>
<tbody>
{% for field in fields|dictsort:"name" %}
<tr>
    <td>{{ field.name }}</td>
    <td>{{ field.data_type }}</td>
    <td>{{ field.verbose }}{% if field.help_text %} - {{ field.help_text|safe }}{% endif %}</td>
</tr>
{% endfor %}
</tbody>
</table>
</div>

{% if methods %}
<h3>{% translate 'Methods with arguments' %}</h3>
<div class="module">
<table class="model">
<thead>
<tr>
    <th scope="col">{% translate 'Method' %}</th>
    <th scope="col">{% translate 'Arguments' %}</th>
    <th scope="col">{% translate 'Description' %}</th>
</tr>
</thead>
<tbody>
{% for method in methods|dictsort:"name" %}
<tr>
    <td>{{ method.name }}</td>
    <td>{{ method.arguments }}</td>
    <td>{{ method.verbose }}</td>
</tr>
{% endfor %}
</tbody>
</table>
</div>
{% endif %}

<p><a href="{% url 'django-admindocs-models-index' %}">&lsaquo; {% translate 'Back to Model documentation' %}</a></p>
</div>
{% endblock %}
